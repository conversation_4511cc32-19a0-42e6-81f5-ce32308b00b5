# Chart Dirty Tracking Implementation Guide

## Overview

The chart page now includes comprehensive dirty tracking functionality that:
- Tracks changes to form fields on a per-tab basis
- Shows visual indicators on tab headers when they contain unsaved changes
- Enables/disables the save button based on whether any changes exist
- Provides granular dirty state information for each tab

## Architecture

### Components

1. **Tab Field Mapping Configuration** (`src/features/chart/config/tabFieldMapping.ts`)
   - Centralized configuration mapping form fields to their respective tabs
   - Helper functions for field-to-tab lookups

2. **Dirty Tracking Hook** (`src/features/chart/hooks/ui/useChartUIState.ts`)
   - `useTabDirtyTracking()` - Core hook for tracking dirty state per tab
   - `useChartUIStateWithDirtyTracking()` - Enhanced UI state hook with dirty tracking

3. **Visual Indicators**
   - Tab headers show red dots (●) when they contain unsaved changes
   - Save button is only enabled when changes exist

## Usage

### Basic Usage

```typescript
import { useChartUIStateWithDirtyTracking } from '../../hooks/ui/useChartUIState';

function ChartComponent() {
  const {
    selectedTab,
    handleTabChange,
    isEdTabDirty,
    isObsTabDirty,
    isProfeeTabDirty,
    isAnyTabDirty
  } = useChartUIStateWithDirtyTracking();

  return (
    <Tabs value={selectedTab} onChange={handleTabChange}>
      <Tab label={`ED ${isEdTabDirty ? '●' : ''}`} />
      <Tab label={`Obs ${isObsTabDirty ? '●' : ''}`} />
      <Tab label={`Profee ${isProfeeTabDirty ? '●' : ''}`} />
    </Tabs>
  );
}
```

### Field Mapping

Fields are mapped to tabs as follows:

- **Shared Fields**: `visitId`, `firstName`, `lastName`, etc. (header fields)
- **ED Tab**: `treatmentArea`, `edChartStatus`, `note`, `levelCheckboxStates`
- **Obs Tab**: `obsStart`, `obsEnd`, `isObs`
- **Profee Tab**: `traumaActivation`, `criticalCareMins`, `mod25`, `mod59`

### Adding New Fields

To add a new field to dirty tracking:

1. Add the field to the appropriate tab in `tabFieldMapping.ts`
2. The dirty tracking will automatically include it

```typescript
// In tabFieldMapping.ts
export const TAB_FIELD_MAPPING = {
  ed: [
    'treatmentArea',
    'edChartStatus', 
    'note',
    'levelCheckboxStates',
    'newEdField' // Add new field here
  ],
  // ...
};
```

## Implementation Details

### Dirty State Detection

The system uses React Hook Form's `dirtyFields` to detect changes:
- Compares current form values with initial values
- Tracks nested objects (like `levelCheckboxStates`) properly
- Updates in real-time as user makes changes

### Save Button Logic

```typescript
<Button 
  disabled={!isAnyTabDirty}
  onClick={handleSave}
>
  Save
</Button>
```

The save button is:
- **Enabled** when any tab has unsaved changes
- **Disabled** when no changes exist
- **Visually dimmed** when disabled

### Visual Indicators

Tab headers show a red dot (●) when dirty:
```typescript
<Tab 
  label={
    <span>
      ED
      {isEdTabDirty && (
        <span style={{ color: '#f44336', marginLeft: '4px' }}>●</span>
      )}
    </span>
  } 
/>
```

## Testing

Tests are included for:
- Field mapping configuration
- Dirty tracking hook functionality
- Tab-to-field relationships

Run tests with:
```bash
npm test tabFieldMapping
```

## Future Enhancements

Potential improvements:
1. **Save Confirmation**: Warn users before navigating away with unsaved changes
2. **Auto-save**: Implement periodic auto-save functionality
3. **Field-level Indicators**: Show which specific fields are dirty
4. **Undo/Redo**: Track change history for undo functionality
5. **Save Status**: Show saving/saved status feedback
