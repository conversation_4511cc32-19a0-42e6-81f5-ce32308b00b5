import { useEffect, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import type { ChartFormValues } from '../data/useChartFormData';
import { chartLogger } from '../../../../utils/logger';

/**
 * Maps form field names to their corresponding tab indexes
 * This determines which tab gets marked as dirty when specific fields change
 */
const FIELD_TO_TAB_MAP: Record<string, number> = {
  // ED Tab (index 0) fields
  'treatmentArea': 0,
  'edChartStatus': 0,
  'note': 0,
  'levelCheckboxStates': 0,

  // Obs Tab (index 1) fields  
  'obsStart': 1,
  'obsEnd': 1,

  // <PERSON>ee <PERSON>b (index 2) fields
  'provider': 2,
  'criticalCareMins': 2,
  'traumaActivation': 2,
  'specialNoCharge': 2,
  'mod25': 2,
  'mod59': 2,

  // Shared header fields - these affect all tabs
  'visitId': -1,
  'medicalRecordNumber': -1,
  'lastName': -1,
  'firstName': -1,
  'dob': -1,
  'age': -1,
  'edDos': -1,
  'edStart': -1,
  'edEnd': -1,
  'isEd': -1,
  'isObs': -1,
  'dischargeStatus': -1,
};

export interface ChartDirtyState {
  /** Overall form dirty state */
  isDirty: boolean;
  /** Per-tab dirty state */
  tabDirtyState: Record<number, boolean>;
  /** Whether any tab is dirty (used for save button) */
  hasAnyDirtyTab: boolean;
  /** List of dirty field names for debugging */
  dirtyFields: string[];
}

/**
 * Hook to track dirty state for the chart form, both overall and per-tab
 * 
 * This hook:
 * - Tracks which form fields have been modified
 * - Maps dirty fields to their corresponding tabs
 * - Provides per-tab dirty indicators
 * - Enables save button only when changes exist
 * 
 * @returns ChartDirtyState object with dirty tracking information
 */
export const useChartDirtyTracking = (): ChartDirtyState => {
  const formContext = useFormContext<ChartFormValues>();
  
  // Gracefully handle when hook is called outside FormProvider
  if (!formContext) {
    chartLogger.warn('useChartDirtyTracking called outside FormProvider context');
    return {
      isDirty: false,
      tabDirtyState: { 0: false, 1: false, 2: false },
      hasAnyDirtyTab: false,
      dirtyFields: [],
    };
  }
  
  const { formState } = formContext;
  const { isDirty, dirtyFields } = formState;

  // Calculate per-tab dirty state
  const tabDirtyState = useMemo(() => {
    const tabState: Record<number, boolean> = {
      0: false, // ED tab
      1: false, // Obs tab  
      2: false, // Profee tab
    };

    // Check each dirty field and mark its corresponding tab as dirty
    Object.keys(dirtyFields || {}).forEach(fieldName => {
      const tabIndex = FIELD_TO_TAB_MAP[fieldName];
      
      if (tabIndex !== undefined && tabIndex >= 0) {
        tabState[tabIndex] = true;
      } else if (tabIndex === -1) {
        // Shared header fields affect all tabs
        tabState[0] = true;
        tabState[1] = true;
        tabState[2] = true;
      } else {
        // Log unmapped fields for debugging
        chartLogger.debug('Unmapped dirty field detected', { fieldName });
      }
    });

    return tabState;
  }, [dirtyFields]);

  // Calculate if any tab is dirty
  const hasAnyDirtyTab = useMemo(() => {
    return Object.values(tabDirtyState).some(Boolean);
  }, [tabDirtyState]);

  // Create list of dirty field names
  const dirtyFieldNames = useMemo(() => {
    return Object.keys(dirtyFields || {});
  }, [dirtyFields]);

  // Debug logging for dirty state changes
  useEffect(() => {
    if (isDirty) {
      chartLogger.debug('Chart dirty state changed', {
        isDirty,
        dirtyFields: dirtyFieldNames,
        tabDirtyState,
        hasAnyDirtyTab
      });
    }
  }, [isDirty, dirtyFieldNames, tabDirtyState, hasAnyDirtyTab]);

  return {
    isDirty,
    tabDirtyState,
    hasAnyDirtyTab,
    dirtyFields: dirtyFieldNames,
  };
};

/**
 * Hook to get dirty state for a specific tab
 * 
 * @param tabIndex - The tab index to check (0 = ED, 1 = Obs, 2 = Profee)
 * @returns boolean indicating if the specified tab is dirty
 */
export const useTabDirtyState = (tabIndex: number): boolean => {
  const { tabDirtyState } = useChartDirtyTracking();
  return tabDirtyState[tabIndex] || false;
};
