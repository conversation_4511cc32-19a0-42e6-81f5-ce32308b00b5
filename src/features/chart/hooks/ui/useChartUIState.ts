import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import type { ChartFormValues } from '../data/useChartFormData';
import { TAB_FIELD_MAPPING } from '../../config/tabFieldMapping';

/**
 * Hook to track dirty state for specific tabs
 * Monitors form fields associated with each tab to determine if changes have been made
 * Must be used within a FormProvider context
 */
export const useTabDirtyTracking = () => {
  const { formState: { dirtyFields } } = useFormContext<ChartFormValues>();

  // Helper function to check if any fields in a group are dirty
  const isTabDirty = (tabFields: readonly string[]) => {
    return tabFields.some(field => {
      if (field === 'levelCheckboxStates') {
        // Special handling for nested object
        return dirtyFields.levelCheckboxStates &&
               Object.keys(dirtyFields.levelCheckboxStates || {}).length > 0;
      }
      return dirtyFields[field as keyof typeof dirtyFields];
    });
  };

  const isEdTabDirty = isTabDirty(TAB_FIELD_MAPPING.ed);
  const isObsTabDirty = isTabDirty(TAB_FIELD_MAPPING.obs);
  const isProfeeTabDirty = isTabDirty(TAB_FIELD_MAPPING.profee);
  const isSharedDirty = isTabDirty(TAB_FIELD_MAPPING.shared);

  // Overall dirty state - any tab or shared fields are dirty
  const isAnyTabDirty = isEdTabDirty || isObsTabDirty || isProfeeTabDirty || isSharedDirty;

  return {
    isEdTabDirty,
    isObsTabDirty,
    isProfeeTabDirty,
    isSharedDirty,
    isAnyTabDirty,
    tabDirtyStates: {
      ed: isEdTabDirty,
      obs: isObsTabDirty,
      profee: isProfeeTabDirty,
      shared: isSharedDirty
    }
  };
};

/**
 * Hook to manage chart page UI state
 * Extracts tab, accordion, and modal state management from ChartPage
 */
export const useChartUIState = () => {
  // Tab state management
  const [selectedTab, setSelectedTab] = useState(0);

  // E&M Level inline state
  const [isEAndMLevelInlineOpen, setIsEAndMLevelInlineOpen] = useState(false);

  // Handle tab changes
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  // Toggle E&M level inline display
  const handleToggleEAndMLevelInline = () => {
    setIsEAndMLevelInlineOpen(prev => !prev);
  };

  return {
    // Tab state
    selectedTab,
    setSelectedTab,
    handleTabChange,

    // E&M Level state
    isEAndMLevelInlineOpen,
    setIsEAndMLevelInlineOpen,
    handleToggleEAndMLevelInline,
  };
};

/**
 * Enhanced chart UI state hook that includes dirty tracking
 * Use this hook when you need both UI state and dirty tracking information
 */
export const useChartUIStateWithDirtyTracking = () => {
  const uiState = useChartUIState();
  const dirtyTracking = useTabDirtyTracking();

  return {
    ...uiState,
    ...dirtyTracking,
  };
};

/**
 * Hook specifically for E&M Level inline state management
 * Can be used in components that only need E&M state
 */
export const useEMInlineState = (defaultOpen: boolean = false) => {
  const [isEAndMLevelInlineOpen, setIsEAndMLevelInlineOpen] = useState(defaultOpen);

  const handleToggleEAndMLevelInline = () => {
    setIsEAndMLevelInlineOpen(prev => !prev);
  };

  return {
    isEAndMLevelInlineOpen,
    setIsEAndMLevelInlineOpen,
    handleToggleEAndMLevelInline,
  };
}; 