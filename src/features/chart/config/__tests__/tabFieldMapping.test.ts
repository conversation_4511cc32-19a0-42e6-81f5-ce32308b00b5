import { 
  TAB_FIELD_MAPPING, 
  getFieldsForTab, 
  getTab<PERSON>or<PERSON>ield, 
  getAllFormFields 
} from '../tabFieldMapping';

describe('tabFieldMapping', () => {
  describe('TAB_FIELD_MAPPING', () => {
    it('should have all required tab configurations', () => {
      expect(TAB_FIELD_MAPPING).toHaveProperty('shared');
      expect(TAB_FIELD_MAPPING).toHaveProperty('ed');
      expect(TAB_FIELD_MAPPING).toHaveProperty('obs');
      expect(TAB_FIELD_MAPPING).toHaveProperty('profee');
    });

    it('should have expected fields for each tab', () => {
      expect(TAB_FIELD_MAPPING.shared).toContain('visitId');
      expect(TAB_FIELD_MAPPING.shared).toContain('firstName');
      expect(TAB_FIELD_MAPPING.shared).toContain('lastName');
      
      expect(TAB_FIELD_MAPPING.ed).toContain('treatmentArea');
      expect(TAB_FIELD_MAPPING.ed).toContain('edChartStatus');
      expect(TAB_FIELD_MAPPING.ed).toContain('levelCheckboxStates');
      
      expect(TAB_FIELD_MAPPING.obs).toContain('obsStart');
      expect(TAB_FIELD_MAPPING.obs).toContain('obsEnd');
      
      expect(TAB_FIELD_MAPPING.profee).toContain('traumaActivation');
      expect(TAB_FIELD_MAPPING.profee).toContain('mod25');
    });
  });

  describe('getFieldsForTab', () => {
    it('should return correct fields for each tab', () => {
      const edFields = getFieldsForTab('ed');
      expect(edFields).toContain('treatmentArea');
      expect(edFields).toContain('note');
      
      const obsFields = getFieldsForTab('obs');
      expect(obsFields).toContain('obsStart');
      expect(obsFields).toContain('obsEnd');
    });
  });

  describe('getTabForField', () => {
    it('should return correct tab for known fields', () => {
      expect(getTabForField('treatmentArea')).toBe('ed');
      expect(getTabForField('obsStart')).toBe('obs');
      expect(getTabForField('mod25')).toBe('profee');
      expect(getTabForField('visitId')).toBe('shared');
    });

    it('should return null for unknown fields', () => {
      expect(getTabForField('unknownField')).toBe(null);
    });
  });

  describe('getAllFormFields', () => {
    it('should return all fields from all tabs', () => {
      const allFields = getAllFormFields();
      
      expect(allFields).toContain('visitId'); // shared
      expect(allFields).toContain('treatmentArea'); // ed
      expect(allFields).toContain('obsStart'); // obs
      expect(allFields).toContain('mod25'); // profee
    });

    it('should not have duplicate fields', () => {
      const allFields = getAllFormFields();
      const uniqueFields = [...new Set(allFields)];
      
      expect(allFields.length).toBe(uniqueFields.length);
    });
  });
});
